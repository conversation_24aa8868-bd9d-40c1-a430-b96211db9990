import sys
import os
sys.path.append('Software/Auto_Focus')

from Z_Interpolation import ZInterpolation

def test_interpolation_with_different_offsets():
    """Test interpolation with different offset values"""

    # Simulate realistic mapping data (like from your actual test)
    grbl_start = (36.61, 3.07)
    grbl_end = (42.23, 7.88)

    # Calculate the correct Y coordinates for 3x3 grid
    import numpy as np
    x_coords = np.linspace(grbl_start[0], grbl_end[0], 3)
    y_coords = np.linspace(grbl_start[1], grbl_end[1], 3)

    print(f"Calculated grid coordinates:")
    print(f"X: {[f'{x:.2f}' for x in x_coords]}")
    print(f"Y: {[f'{y:.2f}' for y in y_coords]}")

    mapping_results = {
        (x_coords[0], y_coords[0]): 10.0707,  # Point 1 (top-left)
        (x_coords[1], y_coords[0]): 10.0442,  # Point 2 (top-center)
        (x_coords[2], y_coords[0]): 10.0416,  # Point 3 (top-right)
        (x_coords[0], y_coords[1]): 10.0570,  # Point 4 (middle-left)
        (x_coords[1], y_coords[1]): 10.0556,  # Point 5 (center)
        (x_coords[2], y_coords[1]): 10.0537,  # Point 6 (middle-right)
        (x_coords[0], y_coords[2]): 10.0830,  # Point 7 (bottom-left)
        (x_coords[1], y_coords[2]): 10.0697,  # Point 8 (bottom-center)
        (x_coords[2], y_coords[2]): 10.0961   # Point 9 (bottom-right)
    }

    print("="*80)
    print("Z INTERPOLATION TEST WITH REALISTIC DATA")
    print("="*80)
    print(f"ROI: X=[{grbl_start[0]:.2f}, {grbl_end[0]:.2f}], Y=[{grbl_start[1]:.2f}, {grbl_end[1]:.2f}]")
    print(f"Original 3x3 mapping points: {len(mapping_results)}")

    # Test different offset values
    offsets = [0.5, 0.25, 0.1]

    for offset in offsets:
        print(f"\n" + "="*60)
        print(f"TESTING WITH OFFSET = {offset}")
        print("="*60)

        z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results, offset=offset)

        if z_interp.create_interpolation_grid():
            print("✓ Interpolation successful!")

            # Show detailed results
            print(z_interp.log_interpolation_results())

            # Test some specific positions
            print(f"\nTesting specific positions with offset {offset}:")
            test_positions = [
                (37.0, 4.0),   # Between grid points
                (40.0, 6.0),   # Between grid points
                (41.0, 7.0),   # Between grid points
            ]

            for x, y in test_positions:
                z = z_interp.get_z_at_position(x, y)
                if z:
                    print(f"  Position ({x:.1f}, {y:.1f}) -> Z = {z:.4f}")
                else:
                    print(f"  Position ({x:.1f}, {y:.1f}) -> ERROR")
        else:
            print("❌ Interpolation failed!")

def test_with_simple_data():
    """Test with simple data for verification"""
    print("\n" + "="*80)
    print("SIMPLE TEST FOR VERIFICATION")
    print("="*80)

    grbl_start = (0.0, 0.0)
    grbl_end = (4.0, 4.0)

    # Simple linear gradient for easy verification
    mapping_results = {
        (0.0, 0.0): 10.0,
        (2.0, 0.0): 10.2,
        (4.0, 0.0): 10.4,
        (0.0, 2.0): 10.1,
        (2.0, 2.0): 10.3,
        (4.0, 2.0): 10.5,
        (0.0, 4.0): 10.2,
        (2.0, 4.0): 10.4,
        (4.0, 4.0): 10.6
    }

    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results, offset=0.5)

    if z_interp.create_interpolation_grid():
        print("✓ Simple interpolation successful!")
        print(z_interp.log_interpolation_results())
    else:
        print("❌ Simple interpolation failed!")

if __name__ == "__main__":
    test_interpolation_with_different_offsets()
    test_with_simple_data()
