"""
Test script for Z Interpolation functionality
Run this to verify all components work correctly
"""

import numpy as np
import sys
import os

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Z_Interpolation import ZInterpolation

def test_basic_interpolation():
    """Test basic interpolation functionality"""
    print("="*60)
    print("TEST 1: Basic Z Interpolation")
    print("="*60)
    
    # Test data - simulate realistic Z values
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Create test mapping results with slight Z variation
    base_z = 10.0
    mapping_results = {}
    
    # Generate 3x3 grid with realistic Z variation
    for i in range(3):
        for j in range(3):
            x = j * 1.5  # 0, 1.5, 3.0
            y = i * 1.5  # 0, 1.5, 3.0
            
            # Add some realistic variation (surface not perfectly flat)
            z_variation = 0.1 * np.sin(x/3 * np.pi) + 0.05 * np.cos(y/3 * np.pi)
            z_value = base_z + z_variation
            
            mapping_results[(x, y)] = z_value
            print(f"Original point ({x:.1f}, {y:.1f}) -> Z = {z_value:.4f}")
    
    # Test interpolation
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    
    if z_interp.create_interpolation_grid():
        print("\nV Interpolation successful!")
        
        # Test specific positions
        test_positions = [
            (0.6, 0.6),   # Between grid points
            (1.5, 1.5),   # Exact center point
            (2.4, 2.4),   # Between grid points
        ]
        
        print("\nTesting specific positions:")
        for x, y in test_positions:
            z = z_interp.get_z_at_position(x, y)
            print(f"  Position ({x:.1f}, {y:.1f}) -> Z = {z:.4f}")
        
        return True
    else:
        print("X Interpolation failed!")
        return False

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n" + "="*60)
    print("TEST 2: Edge Cases and Error Handling")
    print("="*60)
    
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Test with missing points
    print("Testing with missing mapping points...")
    incomplete_results = {
        (0.0, 0.0): 10.1,
        (1.5, 1.5): 10.2,  # Only 2 points instead of 9
    }
    
    z_interp = ZInterpolation(grbl_start, grbl_end, incomplete_results)
    success = z_interp.create_interpolation_grid()
    print(f"Incomplete data result: {'V Handled gracefully' if not success else 'X Should have failed'}")
    
    # Test with invalid Z values
    print("\nTesting with invalid Z values...")
    invalid_results = {}
    for i in range(3):
        for j in range(3):
            x = j * 1.5
            y = i * 1.5
            z = 10.0 if (i == 1 and j == 1) else 0.0  # Only center point valid
            invalid_results[(x, y)] = z
    
    z_interp2 = ZInterpolation(grbl_start, grbl_end, invalid_results)
    success2 = z_interp2.create_interpolation_grid()
    print(f"Invalid Z values result: {'V Handled gracefully' if not success2 else 'X Should have failed'}")
    
    # Test out-of-bounds queries
    print("\nTesting out-of-bounds position queries...")
    valid_results = {(i*1.5, j*1.5): 10.0 + i*0.1 + j*0.05 for i in range(3) for j in range(3)}
    z_interp3 = ZInterpolation(grbl_start, grbl_end, valid_results)
    z_interp3.create_interpolation_grid()
    
    out_of_bounds_positions = [
        (-1.0, 1.0),  # X too low
        (4.0, 1.0),   # X too high
        (1.0, -1.0),  # Y too low
        (1.0, 4.0),   # Y too high
    ]
    
    for x, y in out_of_bounds_positions:
        z = z_interp3.get_z_at_position(x, y)
        result = "V Handled" if z is None else f"X Got {z:.4f}"
        print(f"  Position ({x:.1f}, {y:.1f}) -> {result}")

def test_grid_transformation():
    """Test the grid transformation from 3x3 to 6x6"""
    print("\n" + "="*60)
    print("TEST 3: Grid Transformation Verification")
    print("="*60)
    
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Create perfect linear gradient for testing
    mapping_results = {}
    for i in range(3):
        for j in range(3):
            x = j * 1.5
            y = i * 1.5
            z = 10.0 + x * 0.1 + y * 0.05  # Linear gradient
            mapping_results[(x, y)] = z
    
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    z_interp.create_interpolation_grid()
    
    # Verify grid dimensions
    all_points = z_interp.get_interpolated_grid_points()
    print(f"Total interpolated points: {len(all_points)} (expected: 49)")
    
    # Check grid spacing
    x_coords = sorted(set(x for x, y in all_points.keys()))
    y_coords = sorted(set(y for x, y in all_points.keys()))
    
    print(f"X coordinates: {len(x_coords)} points from {x_coords[0]:.1f} to {x_coords[-1]:.1f}")
    print(f"Y coordinates: {len(y_coords)} points from {y_coords[0]:.1f} to {y_coords[-1]:.1f}")
    
    if len(x_coords) == 7 and len(y_coords) == 7:
        x_spacing = x_coords[1] - x_coords[0]
        y_spacing = y_coords[1] - y_coords[0]
        print(f"V Grid spacing: X={x_spacing:.1f}, Y={y_spacing:.1f}")
    else:
        print(f"X Incorrect grid dimensions: {len(x_coords)}x{len(y_coords)}")
    
    # Test interpolation accuracy for linear case
    print("\nTesting interpolation accuracy (linear case):")
    test_points = [
        (0.6, 0.6),   # Should be interpolatable
        (2.4, 1.2),   # Should be interpolatable
    ]
    
    for x, y in test_points:
        interpolated_z = z_interp.get_z_at_position(x, y)
        expected_z = 10.0 + x * 0.1 + y * 0.05  # Linear formula
        error = abs(interpolated_z - expected_z)
        print(f"  Position ({x:.1f}, {y:.1f}): Expected={expected_z:.4f}, Got={interpolated_z:.4f}, Error={error:.4f}")

def test_performance():
    """Test performance of interpolation"""
    print("\n" + "="*60)
    print("TEST 4: Performance Test")
    print("="*60)
    
    import time
    
    grbl_start = (0.0, 0.0)
    grbl_end = (3.0, 3.0)
    
    # Create test data
    mapping_results = {(i*1.5, j*1.5): 10.0 + np.random.normal(0, 0.1) for i in range(3) for j in range(3)}
    
    # Time interpolation creation
    start_time = time.time()
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    z_interp.create_interpolation_grid()
    creation_time = time.time() - start_time
    
    print(f"Interpolation creation time: {creation_time:.4f} seconds")
    
    # Time multiple queries
    test_positions = [(np.random.uniform(0, 3), np.random.uniform(0, 3)) for _ in range(100)]
    
    start_time = time.time()
    for x, y in test_positions:
        z_interp.get_z_at_position(x, y)
    query_time = time.time() - start_time
    
    print(f"100 position queries time: {query_time:.4f} seconds")
    print(f"Average query time: {query_time/100*1000:.2f} ms")

def run_all_tests():
    """Run all tests"""
    print("Z INTERPOLATION TEST SUITE")
    print("="*60)
    
    tests = [
        ("Basic Interpolation", test_basic_interpolation),
        ("Edge Cases", test_edge_cases),
        ("Grid Transformation", test_grid_transformation),
        ("Performance", test_performance),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, "PASS" if result is not False else "FAIL"))
        except Exception as e:
            print(f"\nX {test_name} failed with error: {e}")
            results.append((test_name, "ERROR"))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    for test_name, result in results:
        status_symbol = "V" if result == "PASS" else "X"
        print(f"{status_symbol} {test_name}: {result}")
    
    passed = sum(1 for _, result in results if result == "PASS")
    total = len(results)
    print(f"\nOverall: {passed}/{total} tests passed")

if __name__ == "__main__":
    run_all_tests()
