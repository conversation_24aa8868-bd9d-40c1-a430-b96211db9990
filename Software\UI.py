# File: UI.py

from PyQt5.QtWidgets import (QWidget, QHBoxLayout, QLabel, QPushButton, QFrame, QSplitter, QGroupBox, QVBoxLayout, QTabWidget,QScrollArea, QTextEdit,
                             QDialog, QComboBox, QTabBar, QGridLayout, QSpacerItem, QSizePolicy, QFileDialog,QMenu, QAction, QMessageBox)
from PyQt5.QtGui import QPixmap, QIcon,  QImage
from PyQt5.QtCore import Qt, QSize, QFileInfo, QThread, pyqtSlot, QMetaObject, Q_ARG, pyqtSignal, QPoint
from Auto_Focus.focus_plot_window import FocusPlotWindow
from Camera.Main_Cam import Main_Camera
from Stitching.StitchingWorker import StitchingWorker
from Camera.Settings import SettingWindow
from Camera.Prev_Cam import USBPreview
from Scale_Adjust import ScaleAdjust
from Grbl.Grbl import Grbl
from Camera.RoiLabel import ROILabel
from Constant import *
from content_widget import AspectRatioLabel
from Auto_Focus.AutoFocus_Worker import AutoFocusWorker
from Auto_Focus.Mapping_AF import MappingAFWorker
from Auto_Focus.Enhanced_Mapping_AF import EnhancedMappingAFWorker # Import worker yang baru
import cv2
from configuration import config

'''
class UI sebagai Orkestrator

class UI dirancang sebagai main Thread yang menampung UI utama (main window), 
berisi berbagai widget dan method nya, serta memiliki komunikasi dengan 
Thread lainnya memalui Signal-Slot Qt. 

Prinsip Desaign:
1. Jaga Antarmuka tetap responsif (Gunakan Thread untuk tugas berat)
2. Hanya menerika data dari Thread lain (Jangan meminta / menarik data)
3. User Action Translation (Hanya mengirim Aksi dari User, tidak dijalankan di UI)
'''

class UI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(config.get("ui.window_title", "Slide Scanner"))
        self.camera_thread = None
        self.camera = None
        self.camera_resolutions = []
        self.scale_adjust = ScaleAdjust(self.camera)
        self.grbl = Grbl()
        self.roi_label = None
        self.last_key = None
        

        # Variabel untuk menyimpan informasi ROI yang terakhir dipilih
        self.last_roi_info = None

        self.valid_keys = valid_keys

        self.setting_window = None 
        self.combo = None 
        
        self.lbl_video = None
        self.lbl_Prev = None
        self.lbl_live_preview_popup = None
        self.autofocus_thread = None
        self.autofocus_worker = AutoFocusWorker(self.camera, self.grbl)
        self.mapping_af_thread = None
        self.mapping_af_worker = None

        # Stitching thread dan worker
        self.stitching_thread = None
        self.stitching_worker = None

        self.connect_grbl_on_startup()
        self.setup_ui()

        # Setup ROI functionality untuk lbl_video (main label)
        if self.lbl_video and isinstance(self.lbl_video, ROILabel):
            self.lbl_video.display_changed.connect(self.on_roi_display_changed)
            self.lbl_video.pixel_clicked.connect(self._on_pixel_clicked)
            # Set Grbl instance untuk Move To functionality
            self.lbl_video.set_grbl_instance(self.grbl)
            print(f"UI: Koneksi sinyal ROILabel (main_label) berhasil. ID: {id(self.lbl_video)}")
        else:
            print("UI: lbl_video bukan ROILabel, sinyal pixel_clicked tidak akan terhubung.")


        # Setup mouse event untuk popup label (selalu untuk swap)
        if self.lbl_live_preview_popup:
            self.lbl_live_preview_popup.mousePressEvent = self._handle_preview_click

        # Setup mouse event untuk main label (untuk ROI ketika preview stream ada di sana)
        if self.lbl_video and hasattr(self.lbl_video, 'mouseReleaseEvent'):
            # Override mouseReleaseEvent untuk update ROI info
            original_mouse_release_main = self.lbl_video.mouseReleaseEvent
            def custom_mouse_release_main(event):
                original_mouse_release_main(event)
                self.update_roi_info()
            self.lbl_video.mouseReleaseEvent = custom_mouse_release_main
  
        # Asumsi USBPreview bisa menerima banyak label.
        # Jika tidak, Anda perlu memodifikasi USBPreview untuk memancarkan sinyal
        # dan menghubungkannya ke slot .setPixmap() dari setiap label.
        self.preview_driver = USBPreview(
            self.lbl_video,
            self.lbl_live_preview_popup,
            cam_index=config.get("camera.preview_cam_index", 0),
        )
        self.setup_main_camera_thread()
        self.setup_custom_tab()
        self.scale_adjust.apply(config.get("camera.default_zoom", "10X"))
        self.plot_window = None

        self.move_to_mode = False

        # Inisialisasi status ROI controls (awalnya nonaktif karena main stream di lbl_video yang tidak bisa ROI)
        self._initialize_roi_controls()

    def _get_active_roi_label(self):
        """
        Mengembalikan ROI label yang aktif berdasarkan kondisi swap.
        ROI hanya bisa dilakukan di main label ketika preview stream ada di sana.
        """
        if self.preview_driver.is_swapped:
            # Preview stream ada di main label (lbl_video), jadi ROI bisa dilakukan di sana
            return self.lbl_video
        else:
            # Main stream ada di main label, preview stream di popup - ROI tidak bisa dilakukan
            return None
        
    
    def setup_main_camera_thread(self):
        self.camera_thread = QThread()
        self.camera = Main_Camera()

        self.camera.moveToThread(self.camera_thread)

        # --- Hubungkan Sinyal dari Worker (Camera) ke Slot di UI ---
        self.camera.camera_opened.connect(self.on_camera_opened)
        self.camera.camera_closed.connect(self.on_camera_closed)
        self.camera.frame_ready.connect(self.update_main_video_feed)
        self.camera.fps_updated.connect(self.lbl_frame.setText)
        self.camera.still_image_saved.connect(self.on_still_image_saved)
        self.camera.error_occurred.connect(self.show_error_message)

        # Hubungkan sinyal start/stop thread
        self.camera_thread.started.connect(self.camera.start)
        # Saat UI ditutup, panggil slot 'stop' di worker
        self.aboutToClose.connect(lambda: QMetaObject.invokeMethod(self.camera, "stop", Qt.QueuedConnection))

        # Mulai thread
        self.camera_thread.start()

    # ======================================================================
    # SLOT PENERIMA DARI WORKER KAMERA
    # ======================================================================
    
    @pyqtSlot(str, list)
    def on_camera_opened(self, camera_name, resolutions):
        """Dipanggil saat kamera berhasil dibuka oleh worker."""
        print(f"UI: Menerima sinyal 'camera_opened'. Nama: {camera_name}")
        self.Name.setText(camera_name)
        self.camera_resolutions = resolutions # Simpan daftar resolusi
        self.Snap.setEnabled(True)
        self.setting.setEnabled(True)
        self.scale_adjust = ScaleAdjust(self.camera)
        self.scale_adjust.apply(config.get("camera.default_zoom", "10X"))

    @pyqtSlot()
    def on_camera_closed(self):
        """Dipanggil saat kamera ditutup atau terputus."""
        print("UI: Menerima sinyal 'camera_closed'. Menonaktifkan kontrol.")
        self.Name.setText("Kamera Terputus")
        self.Snap.setEnabled(False)
        self.setting.setEnabled(False)

    @pyqtSlot(QPixmap)
    def update_main_video_feed(self, pixmap):
        """Menerima QPixmap dari worker dan menampilkannya di label yang sesuai."""
        target_label = self.lbl_live_preview_popup if self.preview_driver.is_swapped else self.lbl_video
        if target_label and not pixmap.isNull():
            target_label.setPixmap(pixmap.scaled(
                target_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation
            ))

    @pyqtSlot(str)
    def on_still_image_saved(self, filename):
        QMessageBox.information(self, "Sukses", f"Gambar berhasil disimpan sebagai:\n{filename}")
        
    @pyqtSlot(str)
    def show_error_message(self, message):
        QMessageBox.critical(self, "Error Kamera", message)

    # ======================================================================
    # PENANGANAN AKSI UI
    # ======================================================================
    
    def handle_snap_button_click(self):
        """
        Logika ini sekarang berada di UI. UI menampilkan menu, lalu mengirim
        perintah 'snap' dengan indeks resolusi yang dipilih ke worker.
        """
        if not self.camera or not self.camera_resolutions:
            self.show_error_message("Kamera tidak siap atau tidak ada resolusi yang tersedia.")
            return

        menu = QMenu(self)
        for i, res_str in enumerate(self.camera_resolutions):
            action = QAction(res_str, self)
            action.setData(i)
            menu.addAction(action)
        
        selected_action = menu.exec(self.mapToGlobal(self.Snap.pos()))

        if selected_action:
            resolution_index = selected_action.data()
            print(f"UI: Meminta snap dengan resolusi indeks {resolution_index}")
            # Panggil slot 'snap' di worker thread dengan aman
            QMetaObject.invokeMethod(self.camera, "snap", Qt.QueuedConnection,
                                     Q_ARG(int, resolution_index))

    def change_setting(self):
        if self.combo and self.scale_adjust:
            pilihan = self.combo.currentText()
            print(f"Zoom diubah menjadi: {pilihan}")
            try:
                # Jika `apply` aman dipanggil, bisa langsung. Jika tidak, gunakan invokeMethod
                self.scale_adjust.apply(pilihan)
            except ValueError as e:
                print(e)
                
    # ======================================================================
    # SIKLUS HIDUP APLIKASI (LIFECYCLE)
    # ======================================================================

    def closeEvent(self, event):
        print("UI: Aplikasi akan ditutup...")
        # Pancarkan sinyal untuk memberi tahu worker agar bersiap berhenti
        self.aboutToClose.emit()
        
        # Hentikan thread preview camera
        if hasattr(self, 'preview_driver') and self.preview_driver:
            self.preview_driver.stop()
            
        # Tunggu thread kamera utama selesai
        if self.camera_thread and self.camera_thread.isRunning():
            print("UI: Menunggu camera_thread berhenti...")
            if not self.camera_thread.wait(3000): # Tunggu max 3 detik
                print("UI: Peringatan, thread kamera tidak berhenti dengan bersih.")
                self.camera_thread.terminate() # Opsi terakhir

        print("UI: Penutupan selesai.")
        super().closeEvent(event)
    
    # Sinyal kustom untuk menangani penutupan
    aboutToClose = pyqtSignal()
    
    def setup_ui(self):
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        splitter = QSplitter(Qt.Horizontal)
        self._setup_left_panel(splitter)
        self._setup_right_panel(splitter)
        splitter.setSizes([1, 5])
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        self._connect_jog_button_signals()
        self._apply_final_ui_settings()
        

    def _setup_left_panel(self, splitter_arg):
        self.camera_container = QFrame()
        self.camera_container.setFrameShape(QFrame.Box)
        container_layout = QVBoxLayout(self.camera_container)
        main_controls_gbox = self._create_main_controls_groupbox()
        container_layout.addWidget(main_controls_gbox)
        splitter_arg.addWidget(self.camera_container)

    def _create_main_controls_groupbox(self):
        gbox = QGroupBox()
        vlyt = QVBoxLayout(gbox)
        self.Name = QPushButton("oke")
        self.open = QPushButton("Open File...")
        self.open.clicked.connect(self.open_file)
        self.Snap = QPushButton("Snap")
        self.Snap.clicked.connect(self.handle_snap_button_click)
        self.Snap.setEnabled(False)
        self.setting = QPushButton("Setting")
        self.setting.clicked.connect(self.open_setting_window)
        vlyt.addWidget(self.Name)
        vlyt.addWidget(self.open)
        vlyt.addWidget(self.Snap)
        vlyt.addWidget(self.setting)
        vlyt.addItem(QSpacerItem(50, 50))
        grbl_controls_layout = self._create_grbl_controls_layout()
        vlyt.addLayout(grbl_controls_layout)
        status_box = self._create_status_layouts()
        vlyt.addWidget(status_box)
        ROI_Box = self._create_ROI_layout()
        vlyt.addWidget(ROI_Box)
        vlyt.addStretch()
        return gbox

    def _create_grbl_controls_layout(self):
        grid = QGridLayout()
        self.btn_up = self.create_button(Icon_up)
        self.btn_down = self.create_button(Icon_down)
        self.btn_right = self.create_button(Icon_right)
        self.btn_left = self.create_button(Icon_left)
        self.btn_zup = self.create_button(Icon_zup)
        self.btn_zdown = self.create_button(Icon_zdown)
        grbl_buttons = [self.btn_up, self.btn_down, self.btn_left, self.btn_right, self.btn_zup, self.btn_zdown]
        for btn in grbl_buttons:
            btn.setFocusPolicy(Qt.NoFocus)

        grid.addWidget(self.btn_up, 0, 1)
        grid.addWidget(self.btn_left, 1, 0)
        grid.addWidget(self.btn_down, 2, 1)
        grid.addWidget(self.btn_right, 1, 2)
        grid.addWidget(self.btn_zup, 0, 4)
        grid.addWidget(self.btn_zdown, 2, 4)
        return grid
    
    def _create_status_layouts(self):
        status_Box = QGroupBox()
        status_layout = QVBoxLayout()
        self.grbl_pos_label = QLabel("X: 0.00  Y: 0.00  Z: 0.00  [Disconnected]")
        self.btn_autofocus = QPushButton("Auto Focus")
        self.btn_autofocus.clicked.connect(self.on_autofocus_clicked)
        self.btn_refine_focus = QPushButton("Refine Focus")
        self.btn_refine_focus.clicked.connect(self.on_refine_focus_clicked)
        self.btn_mapping_af = QPushButton("Mapping AF")
        self.btn_mapping_af.clicked.connect(self.on_mapping_af_clicked)
        self.btn_stitch = QPushButton("Stitch")
        self.btn_stitch.clicked.connect(self.on_stitch_clicked)
        self.btn_stitch.setEnabled(False)  # Disabled until Mapping AF completes

        status_layout.addWidget(self.grbl_pos_label)
        status_layout.addWidget(self.btn_autofocus)
        status_layout.addWidget(self.btn_refine_focus)
        status_layout.addWidget(self.btn_mapping_af)
        status_layout.addWidget(self.btn_stitch)
        status_Box.setLayout(status_layout)
        return status_Box
    
    def _create_ROI_layout(self):
        ROI_Box = QGroupBox()
        ROI_layout = QVBoxLayout()
        self.roi_nom = QLabel("Roi DiPilih : - ")
        self.roi_set = QPushButton("Set ROI")
        self.roi_set.clicked.connect(self.display_roi_info)
        self.roi_crop = QPushButton("Cropping ROI")
        self.roi_crop.clicked.connect(self.crop_roi)
        self.roi_reset = QPushButton("Reset ROI")
        self.roi_reset.clicked.connect(self.reset_roi)
        self.roi_move = QPushButton("Move To")
        self.roi_move.clicked.connect(self._toggle_roi_pixel_mode)

        ROI_layout.addWidget(self.roi_nom)
        ROI_layout.addWidget(self.roi_set)
        ROI_layout.addWidget(self.roi_crop)
        ROI_layout.addWidget(self.roi_reset)
        ROI_layout.addWidget(self.roi_move)
        ROI_Box.setLayout(ROI_layout)
        return ROI_Box

    def _setup_right_panel(self, splitter_arg):
        self.device_container = QFrame()
        self.device_container.setFrameShape(QFrame.Box)
        container_layout = QVBoxLayout(self.device_container)
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab)
        live_stream_tab_widget = self._create_live_stream_tab()
        preview_tab_widget = self._create_preview_tab()
        self.tab_widget.addTab(live_stream_tab_widget, "Live Stream")
        #self.tab_widget.addTab(preview_tab_widget, "Preview Camera")
        tab_bar = self.tab_widget.tabBar()
        tab_bar.setTabButton(0, QTabBar.RightSide, None)
        self.tab_widget.setTabText(0, "")
        container_layout.addWidget(self.tab_widget)
        splitter_arg.addWidget(self.device_container)

    def _create_live_stream_tab(self):
        """ Membuat tab untuk Live Stream dengan pop-up preview yang menjaga rasio aspek. """
        self.live_stream_tab = QWidget()
        live_stream_layout = QVBoxLayout(self.live_stream_tab)

        video_container = QWidget()
        video_container.setFixedSize(
            config.get("ui.video_container_width", 1050),
            config.get("ui.video_container_height", 850)
        )

        # lbl_video adalah main label (besar) - harus bisa ROI ketika preview stream ada di sana
        self.lbl_video = ROILabel(video_container)
        self.lbl_video.setAlignment(Qt.AlignCenter)
        self.lbl_video.setGeometry(0, 0, video_container.width(), video_container.height())
        self.lbl_video.setStyleSheet("background-color: #2d2d2d;")

        # lbl_live_preview_popup adalah popup label (kecil) - hanya untuk swap, bukan ROI
        self.lbl_live_preview_popup = QLabel(video_container)
        self.lbl_live_preview_popup.setFrameShape(QFrame.Box)
        self.lbl_live_preview_popup.setLineWidth(1)
        
        # 3. ATUR UKURAN DENGAN RASIO ASPEK YANG BENAR (misal 4:3)
        # Kebanyakan webcam memiliki rasio 4:3 atau 16:9. Kita gunakan 4:3.
        popup_width = config.get("ui.live_preview_popup_width", 240) # Lebar pop-up
        popup_height = int(popup_width * 3 / 4) # Hitung tinggi agar rasio 4:3 -> 180
        self.lbl_live_preview_popup.setFixedSize(popup_width, popup_height)

        margin = 10
        popup_x = video_container.width() - popup_width - margin
        popup_y = margin
        self.lbl_live_preview_popup.move(popup_x, popup_y)
        self.lbl_live_preview_popup.raise_()

        hbox_video = QHBoxLayout()
        hbox_video.addStretch(1)
        hbox_video.addWidget(video_container)
        hbox_video.addStretch(1)
        
        self.lbl_frame = QLabel()
        self.lbl_frame.setAlignment(Qt.AlignCenter)
        
        live_stream_layout.addLayout(hbox_video)
        live_stream_layout.addWidget(self.lbl_frame)
        
        return self.live_stream_tab

    def _create_preview_tab(self):
        self.Preview_tab = QWidget()
        preview_tab_layout = QVBoxLayout(self.Preview_tab)
        
        # Gunakan AspectRatioLabel juga di sini agar konsisten
        self.lbl_Prev = AspectRatioLabel()
        self.lbl_Prev.setAlignment(Qt.AlignCenter)
        # Tidak perlu setFixedSize agar bisa responsif, atau set jika memang harus
        
        hbox_prev = QHBoxLayout()
        hbox_prev.addStretch(1)
        hbox_prev.addWidget(self.lbl_Prev)
        hbox_prev.addStretch(1)
        preview_tab_layout.addLayout(hbox_prev)
        
        return self.Preview_tab


    def _connect_jog_button_signals(self):
        button_to_key_map = { self.btn_up: Qt.Key_Up, self.btn_down: Qt.Key_Down, self.btn_left: Qt.Key_Left, self.btn_right: Qt.Key_Right, self.btn_zup: Qt.Key_PageUp, self.btn_zdown: Qt.Key_PageDown, }
        for button_widget, qt_key_enum in button_to_key_map.items():
            if qt_key_enum in self.valid_keys:
                direction, value = self.valid_keys[qt_key_enum]
                button_widget.pressed.connect( lambda d=direction, v=value: self.start_jog(d, v) )
                button_widget.released.connect(self.stop_jog)
            else:
                print(f"PERINGATAN: Tombol {button_widget} terpetakan ke key {qt_key_enum} yang tidak ada di self.valid_keys.")

    def _apply_final_ui_settings(self):
        self.setFocusPolicy(Qt.StrongFocus)
        self.setFocus()

    def open_setting_window(self):
        if self.setting_window is None or not self.setting_window.isVisible():
            # Kirim objek kamera (yang ada di worker thread) ke SettingWindow
            self.setting_window = SettingWindow(self.camera, self.grbl, self, preview_camera=self.preview_driver)
        
            # PENTING: Beri tahu SettingWindow tentang resolusi yang sudah kita dapatkan
            if self.camera_resolutions:
                 self.setting_window.populate_resolutions(self.camera_resolutions)
        
            self.setting_window.show()
        else:
            self.setting_window.raise_()
            self.setting_window.activateWindow()
    
    def closeEvent(self, event):
        if hasattr(self, 'preview_driver') and self.preview_driver:
            self.preview_driver.stop()
        super().closeEvent(event)
    
    def setup_custom_tab(self):
        layout = QHBoxLayout()
        label = QLabel("Live Stream:")
        self.combo = QComboBox()
        self.combo.addItems(zoom_size)
        self.combo.currentIndexChanged.connect(self.change_setting)
        layout.addWidget(label)
        layout.addWidget(self.combo)
        layout.setContentsMargins(5, 2, 5, 2) 
        widget = QWidget()
        widget.setLayout(layout)
        self.tab_widget.tabBar().setTabButton(0, QTabBar.LeftSide, widget)
    
    def change_setting(self):
        if self.combo:
            pilihan = self.combo.currentText()
            print(f"Zoom diubah menjadi: {pilihan}")
            try:
                self.scale_adjust.apply(pilihan)
            except ValueError as e:
                print(e)
    
    def connect_grbl_on_startup(self):
        self.grbl.connect_grbl()
        settings_file = config.get("grbl.settings_file", "GRBL_Settings.json")
        grbl_config = self.grbl.load_settings_from_Json(settings_file)
        self.grbl.flush_settings_grbl(grbl_config)
        self.grbl.position_updated.connect(self.update_grbl_position_label)
        
    def create_button(self,icon_path):
        btn = QPushButton()
        btn.setIcon(QIcon(icon_path))
        btn.setIconSize(QSize(config.get("ui.icon_size", 32), config.get("ui.icon_size", 32)))
        return btn
    
    def keyPressEvent(self, event):
        key = event.key()
        if event.isAutoRepeat():
            return
        if key in self.valid_keys:
            self.last_key = key
            direction, value = self.valid_keys[key]
            self.start_jog(direction, value)

    def keyReleaseEvent(self, event):
        key = event.key()
        if event.isAutoRepeat():
            return
        if key == self.last_key:
            self.last_key = None
            self.stop_jog()

    def start_jog(self, direction, value):
        self.grbl.jog(direction, value)

    def stop_jog(self):
        self.grbl.stop_jog()
    
    def close_tab(self, index):
        if index >= 3: 
            widget = self.tab_widget.widget(index)
            widget.deleteLater()
            self.tab_widget.removeTab(index)
            print(f"Tab di indeks {index} ditutup.")
        else:
            print(f"Tab '{self.tab_widget.tabText(index)}' tidak bisa ditutup.")
    
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName( self, "Pilih Gambar", "", "Gambar (*.png *.jpg *.jpeg *.bmp *.gif);;Semua File (*.*)" )
        if file_path:
            file_info = QFileInfo(file_path)
            tab_name = file_info.fileName()
            new_tab_content = QWidget()
            tab_layout = QVBoxLayout(new_tab_content)
            image_label = AspectRatioLabel() # Ganti ke AspectRatioLabel agar gambar di tab juga bagus
            pixmap = QPixmap(file_path)
            if pixmap.isNull():
                image_label.setText("Gagal memuat gambar.")
                image_label.setAlignment(Qt.AlignCenter)
            else:
                image_label.setPixmap(pixmap)
                image_label.setAlignment(Qt.AlignCenter)
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setWidget(image_label)
            tab_layout.addWidget(scroll_area)
            self.tab_widget.addTab(new_tab_content, tab_name)
            self.tab_widget.setCurrentIndex(self.tab_widget.count() - 1)
        else:
            print("Pembukaan file dibatalkan.")
    
    def on_autofocus_clicked(self):
        # Check if mapping AF is running
        if self.mapping_af_thread and self.mapping_af_thread.isRunning():
            QMessageBox.warning(self, "Error", "Mapping AF sedang berjalan. Tunggu hingga selesai atau batalkan terlebih dahulu.")
            return

        if self.autofocus_thread and self.autofocus_thread.isRunning():
            reply = QMessageBox.question(self, "Batal?", "Proses auto focus sedang berjalan. Batalkan?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.autofocus_worker.stop_autofocus()
            return
            
        reply = QMessageBox.question(self, 'Mulai Auto Focus?',
                                     'Ini akan memulai proses auto focus.\nPastikan area sudah siap.',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No: return
        
        self.btn_autofocus.setText("AF Running...")
        self.btn_autofocus.setEnabled(False)
        
        self.autofocus_thread = QThread(self)
        self.autofocus_worker = AutoFocusWorker(self.camera, self.grbl)
        
        # Pindahkan kedua worker ke thread yang sama
        self.autofocus_worker.moveToThread(self.autofocus_thread)
        self.grbl.moveToThread(self.autofocus_thread)
        
        # Hubungkan sinyal dari worker ke UI
        self.autofocus_worker.status_changed.connect(self.lbl_frame.setText)
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished)
        
        # Hubungkan siklus hidup thread
        self.autofocus_thread.started.connect(self.autofocus_worker.run_autofocus)
        self.autofocus_thread.finished.connect(self.autofocus_thread.deleteLater)
        self.autofocus_worker.destroyed.connect(lambda: print("AF Worker dihancurkan."))
        self.autofocus_thread.finished.connect(lambda: setattr(self, 'autofocus_thread', None))
        self.autofocus_worker.curve_data_ready.connect(self.show_focus_plots)
        
        self.autofocus_thread.start()

    # Buat slot baru ini untuk menerima sinyal
    @pyqtSlot(float, float, float, str)
    def update_grbl_position_label(self, x, y, z, status):
        """Memperbarui teks pada label posisi GRBL."""
        pos_text = f"X: {x:.3f}  Y: {y:.3f}  Z: {z:.3f}  [{status}]"
        self.grbl_pos_label.setText(pos_text)
    
    def on_refine_focus_clicked(self):
        """Memulai proses auto focus penyempurnaan (Hill Climbing)."""
        # Check if mapping AF is running
        if self.mapping_af_thread and self.mapping_af_thread.isRunning():
            QMessageBox.warning(self, "Error", "Mapping AF sedang berjalan. Tunggu hingga selesai atau batalkan terlebih dahulu.")
            return

        if self.autofocus_thread and self.autofocus_thread.isRunning():
            QMessageBox.information(self, "Info", "Proses Auto Focus sedang berjalan.")
            return

        # Logikanya hampir sama persis dengan on_autofocus_clicked
        self.btn_autofocus.setEnabled(False)
        self.btn_refine_focus.setEnabled(False)
        self.lbl_frame.setText("Memulai AF Penyempurnaan...")
        
        self.autofocus_thread = QThread(self)
        self.autofocus_worker = AutoFocusWorker(self.camera, self.grbl)
        self.autofocus_worker.moveToThread(self.autofocus_thread)
        self.grbl.moveToThread(self.autofocus_thread)
        
        # Hubungkan sinyal dari worker ke UI (sama seperti sebelumnya)
        self.autofocus_worker.status_changed.connect(self.lbl_frame.setText)
        self.autofocus_worker.focus_finished.connect(self.on_autofocus_finished) # Bisa menggunakan slot finish yang sama
        
        # --- PERBEDAAN UTAMA ADA DI SINI ---
        # Hubungkan sinyal 'started' ke metode 'run_refinement_autofocus'
        self.autofocus_thread.started.connect(self.autofocus_worker.run_refinement_autofocus)
        
        # ... (sisa koneksi finished dan deleteLater tetap sama)
        self.autofocus_thread.finished.connect(self.autofocus_worker.deleteLater)
        self.autofocus_thread.finished.connect(self.autofocus_thread.deleteLater)
        self.autofocus_thread.finished.connect(lambda: setattr(self, 'autofocus_thread', None))
        
        self.autofocus_thread.start()
        
    def on_autofocus_finished(self, best_pos, best_score):
        """Slot ini sekarang menangani hasil dari kedua jenis AF."""
        self.lbl_frame.setText(f"AF Selesai. Fokus terbaik di Z={best_pos:.3f}")

        # Kembalikan GRBL ke thread utama
        self.grbl.moveToThread(self.thread())

        # Force restart GRBL polling untuk update posisi UI
        try:
            if hasattr(self.grbl, 'polling_timer'):
                # Stop dulu jika masih aktif
                if self.grbl.polling_timer.isActive():
                    print("[UI] Stopping existing GRBL polling")
                    self.grbl.stop_polling()

                # Tunggu sebentar untuk memastikan stop
                import time
                time.sleep(0.1)

                # Start ulang polling
                print("[UI] Force restarting GRBL polling after AutoFocus")
                self.grbl.start_polling()

                # Verify polling is running
                if self.grbl.polling_timer.isActive():
                    print("[UI] ✓ GRBL polling successfully restarted")

                    # Ensure signal connection is still active
                    try:
                        self.grbl.position_updated.disconnect()
                    except:
                        pass  # Ignore if already disconnected

                    self.grbl.position_updated.connect(self.update_grbl_position_label)
                    print("[UI] ✓ GRBL position signal reconnected")
                else:
                    print("[UI] ❌ GRBL polling failed to restart")
            else:
                print("[UI] ❌ GRBL polling_timer not found")
        except Exception as e:
            print(f"[UI] Error with GRBL polling after AutoFocus: {e}")

        # Update current position display
        try:
            status, current_x, current_y, current_z = self.grbl.get_status_and_position()
            self.update_grbl_position_label(current_x, current_y, current_z, status)
            print(f"[UI] Position updated after AutoFocus: X={current_x:.3f}, Y={current_y:.3f}, Z={current_z:.3f} [{status}]")
        except Exception as e:
            print(f"[UI] Error updating position after AutoFocus: {e}")

        # Aktifkan kembali kedua tombol
        self.btn_autofocus.setEnabled(True)
        self.btn_refine_focus.setEnabled(True)

        if self.autofocus_thread:
            self.autofocus_thread.quit()
    
    @pyqtSlot(dict, dict)
    def show_focus_plots(self, coarse_data, fine_data):
        if self.plot_window is None or not self.plot_window.isVisible():
            self.plot_window = FocusPlotWindow(self)
        
        # Kirim kedua set data ke jendela plot
        self.plot_window.update_plots(coarse_data, fine_data)
        
        self.plot_window.show()
        self.plot_window.activateWindow()

    def update_roi_info(self):
        """
        Update informasi ROI pada label roi_nom berdasarkan ROI yang dipilih.
        Menggunakan ROI label yang aktif berdasarkan kondisi swap.
        """
        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        roi_size_info = None

        if active_roi_label and hasattr(active_roi_label, 'get_roi_size'):
            roi_size_info = active_roi_label.get_roi_size()

        if roi_size_info and roi_size_info['pixmap_size']:
            # Simpan informasi ROI untuk dipertahankan setelah crop
            self.last_roi_info = roi_size_info

            pixmap_size = roi_size_info['pixmap_size']
            lebar = pixmap_size['width']
            tinggi = pixmap_size['height']

            # Update label dengan informasi ROI
            roi_text = f"ROI Dipilih: {lebar} x {tinggi} px"
            self.roi_nom.setText(roi_text)

            print(f"UI: ROI info updated - {lebar} x {tinggi} piksel")
        else:
            # Jika tidak ada ROI yang valid, tampilkan informasi terakhir jika ada
            if self.last_roi_info and self.last_roi_info['pixmap_size']:
                pixmap_size = self.last_roi_info['pixmap_size']
                lebar = pixmap_size['width']
                tinggi = pixmap_size['height']
                roi_text = f"ROI Terakhir: {lebar} x {tinggi} px"
                self.roi_nom.setText(roi_text)
            else:
                self.roi_nom.setText("ROI Dipilih: -")

    def display_roi_info(self):
        """
        Mengambil data ROI dari ROI label yang aktif dan menampilkannya
        dalam format yang mudah dibaca.
        """
        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        if not active_roi_label:
            QMessageBox.warning(self, "Info", "ROI hanya bisa dipilih ketika preview stream ada di main label. Silakan swap terlebih dahulu.")
            return

        # Pastikan ada ROI yang valid
        if not active_roi_label.roi_rect.isValid():
            QMessageBox.warning(self, "Info", "Silakan pilih area ROI terlebih dahulu.")
            return

        # Update ROI info terlebih dahulu
        self.update_roi_info()

        # Gunakan informasi yang tersimpan atau ambil dari label
        if self.last_roi_info and self.last_roi_info['pixmap_size']:
            pixmap_size = self.last_roi_info['pixmap_size']
            lebar = pixmap_size['width']
            tinggi = pixmap_size['height']

            # Tampilkan informasi detail di MessageBox
            info_text = f"Ukuran ROI: {lebar} x {tinggi} piksel"
            QMessageBox.information(self, "Informasi Ukuran ROI", info_text)

            # Tampilkan di konsol juga
            print("="*40)
            print("      ANALISIS UKURAN ROI (PIKSEL ASLI)      ")
            print("="*40)
            print(f"Besaran ROI (Lebar x Tinggi): {lebar} x {tinggi} piksel")
            print("="*40)
        else:
            QMessageBox.warning(self, "Info", "Tidak ada ROI yang valid untuk dianalisis.")
    
    @pyqtSlot(QPixmap)
    def on_roi_display_changed(self, new_pixmap):
        """
        Dipanggil saat gambar di ROILabel di-crop atau di-reset.
        """
        if not self.preview_driver:
            return
        
        # Jika pixmap kosong, berarti ini adalah reset
        if new_pixmap.isNull():
            print("UI: Menerima sinyal reset, kembali ke streaming normal.")
            self.preview_driver.clear_override()
        else:
            # Jika ada pixmap, berarti ini hasil crop
            print("UI: Menerima gambar crop, mengaktifkan override di Prev_Cam.")
            self.preview_driver.set_override_image(new_pixmap)
    
    @pyqtSlot()
    def _toggle_roi_pixel_mode(self):
        """
        Mengganti mode ROILabel antara ROI Selection dan Pixel Coordinates.
        Menggunakan ROI label yang aktif sebagai target.
        """
        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        if not active_roi_label:
            QMessageBox.warning(self, "Error", "Move To hanya bisa digunakan ketika preview stream ada di main label. Silakan swap terlebih dahulu.")
            return

        if active_roi_label.mode == ROILabel.MODE_ROI_SELECTION:
            active_roi_label.set_mode(ROILabel.MODE_PIXEL_COORDS)
            print("Move To Aktif")
            QMessageBox.information(self, "Mode Aktif", "Mode Pengambilan Koordinat Piksel Aktif.\nKlik pada gambar untuk mendapatkan koordinat piksel.")
        else:
            active_roi_label.set_mode(ROILabel.MODE_ROI_SELECTION)
            print("Move To Tidak Aktif")
            QMessageBox.information(self, "Mode Aktif", "Mode Seleksi ROI Aktif.")

    @pyqtSlot(QPoint)
    def _on_pixel_clicked(self, pixel_coords):
        """Slot untuk menerima dan menampilkan koordinat piksel yang diklik."""
        print(f"Piksel diklik pada koordinat: X={pixel_coords.x()}, Y={pixel_coords.y()}")
        QMessageBox.information(self, "Koordinat Piksel", 
                                f"Anda mengklik pada koordinat piksel:\nX: {pixel_coords.x()}\nY: {pixel_coords.y()}")
    
    def crop_roi(self):
        """
        Crop ROI - menggunakan ROI label yang aktif sebagai target.
        """
        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        if not active_roi_label:
            QMessageBox.warning(self, "Error", "ROI hanya bisa dipilih ketika preview stream ada di main label. Silakan swap terlebih dahulu.")
            return

        # Pastikan ada ROI yang valid
        if not active_roi_label.roi_rect.isValid():
            QMessageBox.warning(self, "Error", "Silakan pilih area ROI terlebih dahulu.")
            return

        # Simpan informasi ROI sebelum crop
        self.update_roi_info()

        # Lakukan crop pada ROI label yang aktif
        active_roi_label.crop_to_roi()

        # Update label untuk menunjukkan bahwa ROI telah di-crop
        if self.last_roi_info and self.last_roi_info['pixmap_size']:
            pixmap_size = self.last_roi_info['pixmap_size']
            lebar = pixmap_size['width']
            tinggi = pixmap_size['height']
            roi_text = f"ROI Cropped: {lebar} x {tinggi} px"
            self.roi_nom.setText(roi_text)

    def reset_roi(self):
        """
        Reset ROI - menggunakan ROI label yang aktif sebagai target.
        """
        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        if not active_roi_label:
            QMessageBox.warning(self, "Error", "ROI hanya bisa direset ketika preview stream ada di main label. Silakan swap terlebih dahulu.")
            return

        active_roi_label.reset_view()

        # Reset informasi ROI
        self.last_roi_info = None
        self.roi_nom.setText("ROI Dipilih: -")
    

    def on_mapping_af_clicked(self):
        # Check if any autofocus process is running
        if self.autofocus_thread and self.autofocus_thread.isRunning():
            QMessageBox.warning(self, "Error", "Auto Focus sedang berjalan. Tunggu hingga selesai atau batalkan terlebih dahulu.")
            return

        if self.mapping_af_thread and self.mapping_af_thread.isRunning():
            reply = QMessageBox.question(self, "Batal?", "Proses Mapping AF sedang berjalan. Batalkan?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                if self.mapping_af_worker:
                    self.mapping_af_worker.stop()
            return

        # Dapatkan ROI label yang aktif
        active_roi_label = self._get_active_roi_label()
        if not active_roi_label:
            QMessageBox.warning(self, "Error", "Mapping AF hanya bisa digunakan ketika preview stream ada di main label. Silakan swap terlebih dahulu.")
            return

        # Pastikan ada ROI yang valid
        if not active_roi_label.roi_rect.isValid():
            QMessageBox.warning(self, "Error", "Silakan pilih area ROI terlebih dahulu.")
            return

        if not self.last_roi_info:
            QMessageBox.warning(self, "Error", "Silakan pilih area ROI terlebih dahulu.")
            return

        reply = QMessageBox.question(self, 'Mulai Mapping AF?',
                                     'Ini akan memulai proses Mapping Auto Focus pada area ROI.\nPastikan area sudah siap.',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No:
            return

        move_controller = active_roi_label.get_move_controller()
        if not move_controller:
            QMessageBox.warning(self, "Error", "MoveToController tidak tersedia.")
            return

        # Dapatkan koordinat piksel dari ROI
        roi_rect_original = active_roi_label._get_roi_in_original_pixmap_coords(active_roi_label.roi_rect)
        
        if roi_rect_original.isNull() or not roi_rect_original.isValid():
            QMessageBox.warning(self, "Error", "Invalid ROI selection.")
            return

        roi_start_pixel = roi_rect_original.topLeft()
        roi_end_pixel = roi_rect_original.bottomRight()

        # Konversi ke koordinat GRBL
        grbl_start = move_controller.pixel_to_grbl_coordinates(roi_start_pixel.x(), roi_start_pixel.y())
        grbl_end = move_controller.pixel_to_grbl_coordinates(roi_end_pixel.x(), roi_end_pixel.y())

        self.btn_mapping_af.setText("Mapping AF Running...")
        self.btn_mapping_af.setEnabled(False)

        self.mapping_af_thread = QThread(self)
        # Gunakan EnhancedMappingAFWorker untuk menyertakan interpolasi Z
        self.mapping_af_worker = EnhancedMappingAFWorker(self.camera, self.grbl, grbl_start, grbl_end)

        # Pindahkan worker utama dan sub-worker (mapping_worker) ke thread
        self.mapping_af_worker.moveToThread(self.mapping_af_thread)
        self.mapping_af_worker.mapping_worker.moveToThread(self.mapping_af_thread)
        # Pastikan autofocus_worker di dalam mapping_worker juga pindah
        if hasattr(self.mapping_af_worker.mapping_worker, 'autofocus_worker'):
            self.mapping_af_worker.mapping_worker.autofocus_worker.moveToThread(self.mapping_af_thread)

        # Connect signals
        self.mapping_af_worker.progress.connect(lambda p, msg: self.lbl_frame.setText(f"Mapping AF: {p}% - {msg}"))
        self.mapping_af_worker.log_message.connect(self.print_log_message)
        self.mapping_af_worker.finished.connect(self.on_mapping_af_finished)

        self.mapping_af_thread.started.connect(self.mapping_af_worker.run)
        self.mapping_af_thread.finished.connect(self.mapping_af_thread.deleteLater)

        self.mapping_af_thread.start()

    def on_mapping_af_finished(self):
        self.lbl_frame.setText("Mapping AF Selesai.")
        self.btn_mapping_af.setText("Mapping AF")
        self.btn_mapping_af.setEnabled(True)

        # Enable Stitch button after Mapping AF completes
        self.btn_stitch.setEnabled(True)

        # Force restart GRBL polling for UI updates
        try:
            if hasattr(self.grbl, 'polling_timer'):
                # Stop dulu jika masih aktif
                if self.grbl.polling_timer.isActive():
                    print("[UI] Stopping existing GRBL polling")
                    self.grbl.stop_polling()

                # Tunggu sebentar untuk memastikan stop
                import time
                time.sleep(0.1)

                # Start ulang polling
                print("[UI] Force restarting GRBL polling after Mapping AF")
                self.grbl.start_polling()

                # Verify polling is running
                if self.grbl.polling_timer.isActive():
                    print("[UI] ✓ GRBL polling successfully restarted")

                    # Ensure signal connection is still active
                    try:
                        self.grbl.position_updated.disconnect()
                    except:
                        pass  # Ignore if already disconnected

                    self.grbl.position_updated.connect(self.update_grbl_position_label)
                    print("[UI] ✓ GRBL position signal reconnected")
                else:
                    print("[UI] ❌ GRBL polling failed to restart")
            else:
                print("[UI] ❌ GRBL polling_timer not found")
        except Exception as e:
            print(f"[UI] Error restarting GRBL polling: {e}")

        # Update current position display
        try:
            status, current_x, current_y, current_z = self.grbl.get_status_and_position()
            self.update_grbl_position_label(current_x, current_y, current_z, status)
            print(f"[UI] Position updated after Mapping AF: X={current_x:.3f}, Y={current_y:.3f}, Z={current_z:.3f} [{status}]")
        except Exception as e:
            print(f"[UI] Error updating position after Mapping AF: {e}")

        if self.mapping_af_thread:
            self.mapping_af_thread.quit()
            self.mapping_af_thread = None
        self.mapping_af_worker = None

    def print_log_message(self, msg):
        print(msg)

    def _handle_preview_click(self, event):
        if event.button() == Qt.LeftButton:
            self.preview_driver.swap_streams()
            self._handle_stream_swap()

    def on_stitch_clicked(self):
        """Handler untuk tombol Stitch"""
        # Check if any process is running
        if self.autofocus_thread and self.autofocus_thread.isRunning():
            QMessageBox.warning(self, "Error", "Auto Focus sedang berjalan. Tunggu hingga selesai atau batalkan terlebih dahulu.")
            return

        if self.mapping_af_thread and self.mapping_af_thread.isRunning():
            QMessageBox.warning(self, "Error", "Mapping AF sedang berjalan. Tunggu hingga selesai atau batalkan terlebih dahulu.")
            return

        if self.stitching_thread and self.stitching_thread.isRunning():
            reply = QMessageBox.question(self, "Batal?", "Proses Stitching sedang berjalan. Batalkan?",
                                         QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                if self.stitching_worker:
                    self.stitching_worker.stop()
            return

        # Check if we have interpolation data from Mapping AF
        if not hasattr(self, 'mapping_af_worker') or self.mapping_af_worker is None:
            QMessageBox.warning(self, "Error", "Tidak ada data Mapping AF. Jalankan Mapping AF terlebih dahulu.")
            return

        if not hasattr(self.mapping_af_worker, 'z_interpolation') or self.mapping_af_worker.z_interpolation is None:
            QMessageBox.warning(self, "Error", "Data interpolasi Z tidak tersedia. Jalankan Mapping AF terlebih dahulu.")
            return

        # Confirm start stitching
        reply = QMessageBox.question(self, 'Mulai Stitching?',
                                     'Ini akan memulai proses Stitching menggunakan hasil interpolasi dari Mapping AF.\n'
                                     'Pastikan area sudah siap dan kamera terhubung.',
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.No:
            return

        # Start stitching process
        self.start_stitching()

    def start_stitching(self):
        """Memulai proses stitching"""
        try:
            self.btn_stitch.setText("Stitching Running...")
            self.btn_stitch.setEnabled(False)

            # Create stitching thread and worker
            self.stitching_thread = QThread(self)
            self.stitching_worker = StitchingWorker(
                self.camera,
                self.grbl,
                self.mapping_af_worker.z_interpolation
            )

            # Move worker to thread
            self.stitching_worker.moveToThread(self.stitching_thread)

            # Connect signals
            self.stitching_worker.progress.connect(lambda p, msg: self.lbl_frame.setText(f"Stitching: {p}% - {msg}"))
            self.stitching_worker.log_message.connect(self.print_log_message)
            self.stitching_worker.finished.connect(self.on_stitching_finished)
            self.stitching_worker.error_occurred.connect(self.show_error_message)

            # Connect thread signals
            self.stitching_thread.started.connect(self.stitching_worker.run)
            self.stitching_thread.finished.connect(self.stitching_thread.deleteLater)

            # Start thread
            self.stitching_thread.start()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Gagal memulai stitching: {e}")
            self.on_stitching_finished()

    def on_stitching_finished(self):
        """Handler ketika stitching selesai"""
        self.lbl_frame.setText("Stitching Selesai.")
        self.btn_stitch.setText("Stitch")
        self.btn_stitch.setEnabled(True)

        # Force restart GRBL polling for UI updates
        try:
            if hasattr(self.grbl, 'polling_timer'):
                if self.grbl.polling_timer.isActive():
                    print("[UI] Stopping GRBL polling before restart")
                    self.grbl.stop_polling()

                # Tunggu sebentar untuk memastikan stop
                import time
                time.sleep(0.1)

                # Start ulang polling
                print("[UI] Force restarting GRBL polling after Stitching")
                self.grbl.start_polling()

                # Verify polling is running
                if self.grbl.polling_timer.isActive():
                    print("[UI] ✓ GRBL polling successfully restarted")
                else:
                    print("[UI] ⚠️ GRBL polling failed to restart")
            else:
                print("[UI] ⚠️ GRBL polling timer not found")
        except Exception as e:
            print(f"[UI] Error restarting GRBL polling after Stitching: {e}")

        # Update current position display
        try:
            status, current_x, current_y, current_z = self.grbl.get_status_and_position()
            self.update_grbl_position_label(current_x, current_y, current_z, status)
            print(f"[UI] Position updated after Stitching: X={current_x:.3f}, Y={current_y:.3f}, Z={current_z:.3f} [{status}]")
        except Exception as e:
            print(f"[UI] Error updating position after Stitching: {e}")

        # Clean up thread and worker
        if self.stitching_thread:
            self.stitching_thread.quit()
            self.stitching_thread = None
        self.stitching_worker = None



    def _handle_stream_swap(self):
        """
        Menangani swap stream antara main camera dan preview camera.

        Cara kerja:
        - Label tetap di posisi fisik yang sama
        - Yang berubah adalah stream mana yang ditampilkan di label mana
        - Ketika is_swapped=True: main camera feed → lbl_live_preview_popup, preview camera → lbl_video (main label)
        - Ketika is_swapped=False: main camera feed → lbl_video (main label), preview camera → lbl_live_preview_popup

        ROI controls hanya aktif ketika preview camera feed ditampilkan di lbl_video (main label)
        karena hanya di main label user bisa melakukan ROI selection dengan nyaman
        """
        is_roi_active = self.preview_driver.is_swapped
        self.roi_set.setEnabled(is_roi_active)
        self.roi_crop.setEnabled(is_roi_active)
        self.roi_reset.setEnabled(is_roi_active)
        self.roi_move.setEnabled(is_roi_active)
        self.btn_mapping_af.setEnabled(is_roi_active)

        # Force a repaint of both labels
        self.lbl_video.update()
        self.lbl_live_preview_popup.update()

        # Update status text
        if is_roi_active:
            print("ROI controls activated - preview camera feed now displayed in main label (lbl_video)")
        else:
            print("ROI controls deactivated - main camera feed displayed in main label (lbl_video)")

    def _initialize_roi_controls(self):
        """
        Inisialisasi status ROI controls berdasarkan posisi awal.
        Awalnya main camera feed di main label, jadi ROI controls nonaktif.
        """
        is_roi_active = False  # Awalnya nonaktif
        self.roi_set.setEnabled(is_roi_active)
        self.roi_crop.setEnabled(is_roi_active)
        self.roi_reset.setEnabled(is_roi_active)
        self.roi_move.setEnabled(is_roi_active)
        self.btn_mapping_af.setEnabled(is_roi_active)
