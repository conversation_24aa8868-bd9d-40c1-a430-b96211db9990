import sys
sys.path.append('Software/Auto_Focus')

from Z_Interpolation import ZInterpolation
import numpy as np

def demo_realistic_interpolation():
    """Demo with realistic data similar to your actual mapping results"""
    
    print("="*80)
    print("DEMO: Z INTERPOLATION WITH REALISTIC DATA")
    print("="*80)
    
    # Use your actual coordinates
    grbl_start = (36.61, 3.07)
    grbl_end = (42.23, 7.88)
    
    # Calculate correct 3x3 grid coordinates
    x_coords = np.linspace(grbl_start[0], grbl_end[0], 3)
    y_coords = np.linspace(grbl_start[1], grbl_end[1], 3)
    
    print(f"ROI: X=[{grbl_start[0]:.2f}, {grbl_end[0]:.2f}], Y=[{grbl_start[1]:.2f}, {grbl_end[1]:.2f}]")
    print(f"3x3 Grid coordinates:")
    print(f"  X: {[f'{x:.2f}' for x in x_coords]}")
    print(f"  Y: {[f'{y:.2f}' for y in y_coords]}")
    
    # Create mapping results with correct coordinates
    mapping_results = {}
    z_values = [10.0707, 10.0442, 10.0416, 10.0570, 10.0556, 10.0537, 10.0830, 10.0697, 10.0961]
    
    idx = 0
    for i in range(3):
        for j in range(3):
            x = x_coords[j]
            y = y_coords[i]
            z = z_values[idx]
            mapping_results[(x, y)] = z
            grid_num = i * 3 + j + 1
            print(f"  Point {grid_num}: ({x:.2f}, {y:.2f}) -> Z={z:.4f}")
            idx += 1
    
    print(f"\nTotal original mapping points: {len(mapping_results)}")
    
    # Test different offset values
    offsets = [0.5, 0.25]
    
    for offset in offsets:
        print(f"\n" + "="*60)
        print(f"INTERPOLATION WITH OFFSET = {offset}")
        print("="*60)
        
        z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results, offset=offset)
        
        if z_interp.create_interpolation_grid():
            print("✓ Interpolation successful!")
            
            # Get all interpolated points
            all_points = z_interp.get_interpolated_grid_points()
            print(f"Total interpolated points: {len(all_points)}")
            
            # Show first 10 points as example
            print("\nFirst 10 interpolated points:")
            count = 0
            for (x, y), z in all_points.items():
                if count < 10:
                    print(f"  Point {count+1}: X={x:.3f}, Y={y:.3f}, Z={z:.4f}")
                    count += 1
                else:
                    break
            
            print(f"  ... and {len(all_points) - 10} more points")
            
            # Test specific positions
            print(f"\nTesting specific positions:")
            test_positions = [
                (38.0, 4.0),   # Between grid points
                (40.0, 6.0),   # Between grid points
                (41.0, 7.0),   # Between grid points
            ]
            
            for x, y in test_positions:
                z = z_interp.get_z_at_position(x, y)
                if z:
                    print(f"  Position ({x:.1f}, {y:.1f}) -> Z = {z:.4f}")
                else:
                    print(f"  Position ({x:.1f}, {y:.1f}) -> ERROR")
                    
            # Show statistics
            z_values = list(all_points.values())
            print(f"\nStatistics:")
            print(f"  Min Z: {min(z_values):.4f}")
            print(f"  Max Z: {max(z_values):.4f}")
            print(f"  Mean Z: {np.mean(z_values):.4f}")
            print(f"  Z Range: {max(z_values) - min(z_values):.4f}")
            
        else:
            print("❌ Interpolation failed!")

def demo_simple_case():
    """Demo with simple case for easy verification"""
    
    print(f"\n" + "="*80)
    print("DEMO: SIMPLE CASE FOR VERIFICATION")
    print("="*80)
    
    grbl_start = (0.0, 0.0)
    grbl_end = (4.0, 4.0)
    
    # Simple linear gradient
    mapping_results = {
        (0.0, 0.0): 10.0,  (2.0, 0.0): 10.2,  (4.0, 0.0): 10.4,
        (0.0, 2.0): 10.1,  (2.0, 2.0): 10.3,  (4.0, 2.0): 10.5,
        (0.0, 4.0): 10.2,  (2.0, 4.0): 10.4,  (4.0, 4.0): 10.6
    }
    
    print("Original 3x3 mapping:")
    for i in range(3):
        row = ""
        for j in range(3):
            x = j * 2.0
            y = i * 2.0
            z = mapping_results[(x, y)]
            row += f"  ({x:.0f},{y:.0f})->Z={z:.1f}"
        print(row)
    
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results, offset=0.5)
    
    if z_interp.create_interpolation_grid():
        print("✓ Simple interpolation successful!")
        
        all_points = z_interp.get_interpolated_grid_points()
        print(f"Total interpolated points: {len(all_points)}")
        
        # Show grid layout
        print("\nInterpolated grid layout:")
        grid_size = int(np.sqrt(len(all_points)))
        x_coords = sorted(set(x for x, y in all_points.keys()))
        y_coords = sorted(set(y for x, y in all_points.keys()))
        
        for y in y_coords:
            row = ""
            for x in x_coords:
                z = all_points.get((x, y), 0)
                row += f" {z:.2f}"
            print(row)
            
    else:
        print("❌ Simple interpolation failed!")

if __name__ == "__main__":
    demo_realistic_interpolation()
    demo_simple_case()
    
    print(f"\n" + "="*80)
    print("DEMO COMPLETE")
    print("="*80)
