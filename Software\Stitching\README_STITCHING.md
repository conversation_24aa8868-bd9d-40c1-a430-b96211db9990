# Stitching System Documentation

## Overview

Sistem Stitching adalah fitur yang memungkinkan pengambilan gambar secara otomatis pada semua titik grid hasil interpolasi dari Mapping AF. Sistem ini menggunakan hasil interpolasi bilinear untuk menentukan posisi Z optimal di setiap titik XY.

## Features

### ✅ Implemented Features

1. **Automatic Grid Navigation**
   - Menggunakan hasil interpolasi 6x6 grid dari Mapping AF
   - Pola pergerakan zigzag untuk efisiensi maksimal
   - Validasi koordinat untuk keamanan

2. **Smart Movement Control**
   - Pergerakan XY terlebih dahulu, kemudian Z
   - Timeout protection untuk setiap gerakan
   - Position verification setelah movement
   - Emergency stop functionality

3. **Image Capture & Storage**
   - Automatic capture dari kamera Toupcam
   - Filename dengan koordinat yang jelas: `stitch_p001_X35.000_Y3.000_Z10.1234_timestamp.png`
   - Folder management dengan timestamp
   - File size validation

4. **Safety & Error Handling**
   - Connection validation (GRBL & Camera)
   - Coordinate range validation
   - Movement distance limits
   - Multiple capture attempts
   - Emergency stop capability

5. **UI Integration**
   - Tombol "Stitch" di bawah "Mapping AF"
   - Progress indicator
   - Status messages
   - Error notifications

## File Structure

```
Software/Stitching/
├── StitchingWorker.py      # Main stitching worker class
├── test_stitching.py       # Test suite
├── README_STITCHING.md     # This documentation
└── stitching_YYYYMMDD_HHMMSS/  # Output folders (auto-created)
    ├── stitch_p001_X35.000_Y3.000_Z10.1234_HHMMSS.png
    ├── stitch_p002_X35.500_Y3.000_Z10.1377_HHMMSS.png
    └── ...
```

## Usage

### Prerequisites

1. **Mapping AF harus sudah selesai**
   - Sistem memerlukan data interpolasi Z dari Mapping AF
   - Tombol Stitch akan aktif setelah Mapping AF selesai

2. **Hardware Ready**
   - Kamera Toupcam terhubung dan running
   - GRBL terhubung dan dalam status Idle
   - Area kerja sudah siap dan aman

### Step-by-Step Usage

1. **Jalankan Mapping AF**
   ```
   1. Set ROI area
   2. Klik "Mapping AF"
   3. Tunggu hingga selesai (tombol "Stitch" akan aktif)
   ```

2. **Mulai Stitching**
   ```
   1. Klik tombol "Stitch"
   2. Konfirmasi dialog
   3. Sistem akan otomatis:
      - Membuat folder output
      - Bergerak ke setiap titik grid
      - Mengambil gambar di setiap posisi
      - Menyimpan dengan nama yang jelas
   ```

3. **Monitor Progress**
   ```
   - Progress bar menunjukkan persentase
   - Status messages di UI
   - Log messages di console
   ```

4. **Emergency Stop**
   ```
   - Klik tombol "Stitch" lagi untuk stop
   - Konfirmasi dialog
   - Sistem akan berhenti dengan aman
   ```

## Technical Details

### Movement Pattern

Sistem menggunakan pola zigzag untuk efisiensi:

```
Grid 6x6 Example:
1 → 2 → 3 → 4 → 5 → 6
                    ↓
12← 11← 10← 9 ← 8 ← 7
↓
13→ 14→ 15→ 16→ 17→ 18
                    ↓
24← 23← 22← 21← 20← 19
...
```

### Safety Limits

```python
# Coordinate ranges (adjust based on your machine)
X_MIN, X_MAX = -5.0, 50.0   # mm
Y_MIN, Y_MAX = -5.0, 50.0   # mm  
Z_MIN, Z_MAX = 0.0, 25.0    # mm

# Movement limits
max_single_move = 10.0      # mm
xy_tolerance = 0.5          # mm
z_tolerance = 0.1           # mm

# Timing
settle_time = 0.5           # seconds after movement
capture_delay = 0.2         # seconds before capture
```

### Error Handling

1. **Connection Errors**
   - GRBL disconnection detection
   - Camera status monitoring
   - Automatic error reporting

2. **Movement Errors**
   - Timeout protection (30s XY, 15s Z)
   - Position verification
   - Distance validation

3. **Capture Errors**
   - Multiple capture attempts (3x)
   - Frame validation
   - File size verification

## Testing

Run the test suite to validate the system:

```bash
cd Software/Stitching
python test_stitching.py
```

Test coverage:
- ✅ StitchingWorker initialization
- ✅ Preparation and validation
- ✅ Coordinate range checking
- ✅ Movement simulation
- ✅ Image capture simulation
- ✅ Emergency stop functionality

## Integration with Existing System

### UI Integration (UI.py)

```python
# Button creation
self.btn_stitch = QPushButton("Stitch")
self.btn_stitch.clicked.connect(self.on_stitch_clicked)
self.btn_stitch.setEnabled(False)  # Enabled after Mapping AF

# Handler implementation
def on_stitch_clicked(self):
    # Validation and confirmation
    # Create StitchingWorker
    # Start stitching process
```

### Data Flow

```
Mapping AF → Z Interpolation → Stitching Worker → Image Capture
     ↓              ↓                ↓               ↓
  3x3 Grid    →  6x6 Grid    →  Movement Plan  →  Saved Images
```

## Troubleshooting

### Common Issues

1. **"Tidak ada data Mapping AF"**
   - Solution: Jalankan Mapping AF terlebih dahulu

2. **"Movement distance too large"**
   - Solution: Periksa posisi awal GRBL, sesuaikan safety limits

3. **"Camera is not running"**
   - Solution: Restart kamera, periksa koneksi

4. **"GRBL connection lost"**
   - Solution: Periksa koneksi serial, restart GRBL

### Debug Mode

Enable debug output:
```python
worker.log_message.connect(lambda msg: print(f"[DEBUG] {msg}"))
worker.error_occurred.connect(lambda msg: print(f"[ERROR] {msg}"))
```

## Future Enhancements

### Planned Features

1. **Advanced Movement Patterns**
   - Spiral pattern option
   - Custom path planning
   - Optimized travel distance

2. **Enhanced Image Processing**
   - Real-time focus verification
   - Image quality assessment
   - Automatic exposure adjustment

3. **Batch Processing**
   - Multiple ROI stitching
   - Automatic image stitching
   - Panorama generation

4. **Performance Optimization**
   - Parallel processing
   - Faster movement algorithms
   - Memory optimization

## Version History

- **v1.0** (2025-01-20): Initial implementation
  - Basic stitching functionality
  - Safety features
  - UI integration
  - Test suite
