"""
Test script for Stitching functionality
Tests the stitching system without actual hardware
"""

import os
import sys
import time
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Auto_Focus.Z_Interpolation import ZInterpolation


class MockCamera:
    """Mock camera for testing"""
    def __init__(self):
        self.is_running = True
        self._last_numpy_frame = self._create_mock_frame()
    
    def _create_mock_frame(self):
        """Create a mock numpy frame"""
        import numpy as np
        # Create a simple 640x480 RGB frame
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        return frame


class MockGRBL:
    """Mock GRBL for testing"""
    def __init__(self):
        self.grbl = MockSerial()
        # Start at a position closer to the interpolation area
        self.current_x = 35.0  # Start near the interpolation area
        self.current_y = 3.0
        self.current_z = 10.0
        
    def move_to(self, x, y):
        """Mock move to XY"""
        print(f"[MOCK GRBL] Moving to X={x:.3f}, Y={y:.3f}")
        self.current_x = x
        self.current_y = y
        time.sleep(0.1)  # Simulate movement time
        
    def move_to_z(self, z):
        """Mock move to Z"""
        print(f"[MOCK GRBL] Moving to Z={z:.4f}")
        self.current_z = z
        time.sleep(0.1)  # Simulate movement time
        
    def wait_for_idle(self, timeout_seconds=30):
        """Mock wait for idle"""
        print(f"[MOCK GRBL] Waiting for idle (timeout: {timeout_seconds}s)")
        time.sleep(0.1)  # Simulate wait time
        return True
        
    def get_current_position(self):
        """Mock get current position"""
        return self.current_x, self.current_y, self.current_z
        
    def stop_jog(self):
        """Mock stop jog"""
        print("[MOCK GRBL] Stop jog called")


class MockSerial:
    """Mock serial connection"""
    def __init__(self):
        self.is_open = True
        
    def write(self, data):
        """Mock write"""
        print(f"[MOCK SERIAL] Write: {data}")


def create_test_interpolation():
    """Create test interpolation data"""
    # Simulate 3x3 mapping results
    grbl_start = (35.0, 3.0)
    grbl_end = (43.0, 9.0)
    
    mapping_results = {
        (35.0, 3.0): 10.1234,  # Point 1 (top-left)
        (39.0, 3.0): 10.2156,  # Point 2 (top-center)  
        (43.0, 3.0): 10.1987,  # Point 3 (top-right)
        (35.0, 6.0): 10.0876,  # Point 4 (middle-left)
        (39.0, 6.0): 10.1543,  # Point 5 (center)
        (43.0, 6.0): 10.2234,  # Point 6 (middle-right)
        (35.0, 9.0): 10.0654,  # Point 7 (bottom-left)
        (39.0, 9.0): 10.1321,  # Point 8 (bottom-center)
        (43.0, 9.0): 10.1876   # Point 9 (bottom-right)
    }
    
    print("Creating test interpolation...")
    z_interp = ZInterpolation(grbl_start, grbl_end, mapping_results)
    
    if z_interp.create_interpolation_grid():
        print("✓ Test interpolation created successfully!")
        return z_interp
    else:
        print("❌ Failed to create test interpolation")
        return None


def test_stitching_worker():
    """Test the StitchingWorker class"""
    print("\n" + "="*60)
    print("TESTING STITCHING WORKER")
    print("="*60)
    
    try:
        # Import StitchingWorker
        from StitchingWorker import StitchingWorker
        
        # Create mock objects
        mock_camera = MockCamera()
        mock_grbl = MockGRBL()
        test_interpolation = create_test_interpolation()
        
        if test_interpolation is None:
            print("❌ Cannot proceed without interpolation data")
            return False
        
        # Create test output folder
        test_folder = f"test_stitching_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create StitchingWorker
        print("\nCreating StitchingWorker...")
        worker = StitchingWorker(
            camera=mock_camera,
            grbl=mock_grbl,
            z_interpolation=test_interpolation,
            output_folder=test_folder
        )

        # Connect signals for debugging
        worker.log_message.connect(lambda msg: print(f"[WORKER LOG] {msg}"))
        worker.error_occurred.connect(lambda msg: print(f"[WORKER ERROR] {msg}"))
        
        # Test preparation
        print("\nTesting preparation...")
        if worker.prepare_stitching():
            print("✓ Preparation successful!")
            print(f"  Total points: {worker.total_points}")
            print(f"  Output folder: {worker.output_folder}")
        else:
            print("❌ Preparation failed!")
            return False
        
        # Test coordinate validation
        print("\nTesting coordinate validation...")
        if worker._validate_coordinate_ranges():
            print("✓ Coordinate validation passed!")
        else:
            print("❌ Coordinate validation failed!")
            return False
        
        # Test movement (first few points only)
        print("\nTesting movement to first 3 points...")
        for i, (x, y, z) in enumerate(worker.grid_points[:3]):
            print(f"\nTesting point {i+1}: ({x:.3f}, {y:.3f}, {z:.4f})")

            # Add debug output
            print(f"  Current GRBL position: {mock_grbl.get_current_position()}")
            print(f"  GRBL connection status: {mock_grbl.grbl.is_open}")

            try:
                result = worker._move_to_position(x, y, z)
                if result:
                    print(f"✓ Movement to point {i+1} successful!")
                else:
                    print(f"❌ Movement to point {i+1} failed!")
                    # Continue testing other points instead of failing completely

            except Exception as e:
                print(f"❌ Exception during movement to point {i+1}: {e}")
                import traceback
                traceback.print_exc()
        
        # Test image capture (mock)
        print("\nTesting image capture...")
        x, y, z = worker.grid_points[0]
        if worker._capture_and_save_image(x, y, z, 0):
            print("✓ Image capture test successful!")
        else:
            print("❌ Image capture test failed!")
            return False
        
        # Test stop functionality
        print("\nTesting stop functionality...")
        worker.stop()
        print("✓ Stop function called successfully!")
        
        print("\n" + "="*60)
        print("✓ ALL STITCHING TESTS PASSED!")
        print("="*60)
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """Test integration with UI components"""
    print("\n" + "="*60)
    print("TESTING INTEGRATION")
    print("="*60)
    
    try:
        # Test import of UI components
        print("Testing imports...")
        
        # This would normally be tested with actual UI
        print("✓ Integration test placeholder - would test with actual UI")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test error: {e}")
        return False


def main():
    """Main test function"""
    print("STITCHING SYSTEM TEST SUITE")
    print("="*60)
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_stitching_worker():
        tests_passed += 1
    
    if test_integration():
        tests_passed += 1
    
    # Summary
    print(f"\n" + "="*60)
    print(f"TEST SUMMARY: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Stitching system is ready.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    print("="*60)


if __name__ == "__main__":
    main()
